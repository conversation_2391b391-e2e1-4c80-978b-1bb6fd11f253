import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from '../constants/urls';

const usePublishDocument = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const publishDocument = async (id: string) => {
        setLoading(true);
        setError(null);
        setSuccess(false);

        let currentToken = token;
        if (!currentToken) {
            await fetchToken();
            currentToken = localStorage.getItem("authToken") || "";
            if (!currentToken) {
                setError("Brak tokenu autoryzacyjnego po odświeżeniu.");
                setLoading(false);
                return;
            }
        }

        const requestBody = {
            publishSchedules: [
                {
                    culture: null
                }
            ]
        };

        try {
            let response = await fetch(`${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}/publish`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${currentToken}`,
                },
                body: JSON.stringify(requestBody),
            });

            if (!response.ok) {
                if (response.status === 401) {
                    await fetchToken();
                    currentToken = localStorage.getItem("authToken") || "";
                    response = await fetch(`${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}/publish`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${currentToken}`,
                        },
                        body: JSON.stringify(requestBody),
                    });
                }
                if (!response.ok) {
                    throw new Error(`Błąd: ${response.statusText}`);
                }
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { publishDocument, loading, error, success };
};

export default usePublishDocument;
