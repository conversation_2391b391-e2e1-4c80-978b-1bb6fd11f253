@import "../../styles/variables";

.filters {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20px;
  grid-area: 1 / 2 / 2 / 6;
  margin: 10px 20px;

  a,
  .button {
    appearance: none;
    background: none;
    border: none;
    outline: none;
    font-size: 20px;
    letter-spacing: 1px;
    font-weight: 600;
    text-align: center;
    padding: 12px 40px;
    margin-right: 10px;
    color: $white;
    cursor: pointer;
    transition: 0.3s ease;
    width: auto;

    &:last-of-type {
      margin-right: 0;
    }

    &:hover {
      filter: brightness(1.2);
    }

    &:focus {
      outline: 2px solid lighten($white, 20%);
      outline-offset: 4px;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      filter: none;
    }
  }

  .publish {
    background-color: $green;
    margin-right: 10px;
  }

  .delete {
    background-color: $red;
  }

  .save {
    background-color: $green;
  }

  .switchesContainer {
    display: flex;
    align-items: center;
    margin: 0px 20px;
    gap: 10px;

    .label {
      font-weight: 500;
      color: $darkGray;
    }

    .switch {
      position: relative;
      display: inline-flex;
      align-items: center;
      width: 44px;
      height: 24px;
      padding: 2px;

      .switchInput {
        opacity: 0;
        width: 0;
        height: 0;
        position: absolute;

        + .switchSlider {
          background-color: $red;

          &:before {
            transform: translateX(0);
          }
        }

        &:checked + .switchSlider {
          background-color: $green;

          &:before {
            transform: translateX(20px);
          }
        }
      }

      .switchSlider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.2s;
        border-radius: 24px;

        &:before {
          position: absolute;
          content: "";
          height: 20px;
          width: 20px;
          left: 2px;
          bottom: 2px;
          background-color: white;
          transition: 0.2s;
          border-radius: 50%;
        }
      }
    }

    .input {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 4px 12px;
      width: 160px;

      &:focus {
        outline: none;
        border-color: #999;
      }
    }
  }
}
