import { useState } from "react";
import styles from "./photographerGrid.module.scss";
import useGetShopPageData from "../../api/useGetShopPageData";
import { UMBRACO_ADRESS } from "../../constants/urls";
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Link } from "react-router-dom";
import useGetPhotographerData from "../../api/useGetPhotographerData";
import Loader from "../loader/Loader";
import MessageAlert from "../messageAlert/MessageAlert";

const PhotographerGrid = () => {
  const { data, isLoading, error } = useGetPhotographerData();
  const [activeSort, setActiveSort] = useState<string>("newest");
  const handleSortChange = (sortType: string) => {
    setActiveSort(sortType);
  };

  if (isLoading) return <Loader />;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  return (
    <div className={styles.photoGrid}>
      <div className={styles.mainContent}>
        <div className={styles.photoGrid}>
          {data
            ?.filter((_: any, index: number) => index !== 0)
            .map((item: any, index: number) => {
              const photographerPhoto = item.properties.image;
              const name = item.name.replace(/\s+/g, '-').toLowerCase();

              if (!photographerPhoto || photographerPhoto.length === 0) {
                return null;
              }

              return (
                <Link
                  to={`/photographers-page/${item.id}`}
                  key={item.id}
                >
                  <div className={styles.photoCard}>
                    <div className={styles.photoImageContainer}>
                      <img
                        src={UMBRACO_ADRESS + photographerPhoto[0].url}
                        alt={photographerPhoto[0].name || "Photo"}
                        className={styles.photoImage}
                      />
                    </div>
                    <div className={`${styles.status} ${styles[item.status]}`}></div>
                    <div className={styles.photoDetails}>
                      <h3>{item.name || "Untitled"}</h3>
                      <p>{item.properties.description}</p>
                    </div>
                  </div>
                </Link>
              );
            })}
        </div>

        <div className={styles.loadMore}>
          <p>MORE</p>
          <KeyboardArrowDownIcon />
        </div>
      </div>
    </div>
  );
};

export default PhotographerGrid;
