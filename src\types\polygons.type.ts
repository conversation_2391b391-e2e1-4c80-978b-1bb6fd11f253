export interface ObjectResult {
  objectId: number;
  polygons: Polygon[];
}

export interface Polygon {
  points: Point[];
}

export interface Point {
  x: number;
  y: number;
}

export interface AddPoint {
  frameIndex: number;
  polygonLists: ObjectResult[];
}

export interface PropagateVideoResponse {
  frameIndex: number;
  results: ObjectResult[];
}

export interface PhotoSelection {
  label: number;
  point: Point;
}

export interface PhotoItem {
  objectId: number;
  title: string;
  polygons: Polygon[];
  selections: PhotoSelection[];
}
