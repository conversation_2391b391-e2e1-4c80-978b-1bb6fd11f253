import styles from './favesPage.module.scss';
import HeaderTraffique from '../../components/headerTraffique/HeaderTraffique';
import FavesGrid from '../../components/favesGrid/FavesGrid';
import FavoriteIcon from '@mui/icons-material/Favorite';

const FavesPage = () => {
    return (
        <div className={styles.photosPage}>
            <HeaderTraffique />
            <div className={styles.contentWrapper}>
                <div className={styles.faves}><FavoriteIcon style={{ width: '40px', height: '40px' }} /> FAVES</div>
                <div className={styles.container}>
                    <FavesGrid />
                </div>
            </div>
        </div>
    );
};

export default FavesPage;
