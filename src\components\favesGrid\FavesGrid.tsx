import { Link } from "react-router-dom";
import useGetShopPageData from "../../api/useGetShopPageData";
import styles from "./favesGrid.module.scss";
import MessageAlert from "../messageAlert/MessageAlert";
import Loader from "../loader/Loader";

const FavesGrid = () => {
    const { data, isLoading, error } = useGetShopPageData();

    const filteredData = data.filter((shopPage: any) => shopPage.properties.shopPagePhoto?.[0]?.url);

    return (
        <>
            {isLoading && <Loader />}
            {(!data || data.length === 0) && <MessageAlert type="error" message="No data available" />}
            {error && <MessageAlert type="error" message="Error loading data" />}
            <div className={styles.masonryGrid}>
                {filteredData.map((item: any) => {
                    const imgSrc = item.properties.shopPagePhoto?.[0]?.url
                        ? `https://duetprodumbraco.azurewebsites.net${item.properties.shopPagePhoto[0].url}`
                        : '';
                    const title = item.name || 'Untitled';
                    return (
                        <Link to={'/photos-page/' + title.toLowerCase().replace(/\s+/g, '-')} state={{ item }} key={item.id}>
                            <div className={styles.gridItem}>
                                <img src={imgSrc} alt={title} />
                                <div className={styles.textBlock}>
                                    <h1>{title}</h1>
                                    <p>VIEWS 140</p>
                                </div>
                            </div>
                        </Link>
                    );
                })}
            </div>
        </>
    );
};

export default FavesGrid;