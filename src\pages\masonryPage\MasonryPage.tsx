import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import Search from '../../components/search/Search';
import MasonryGrid from '../../components/masonryGrid/MasonryGrid';
import styles from './masonryPage.module.scss';
import HeaderTraffique from '../../components/headerTraffique/HeaderTraffique';

const MasonryPage = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const initialQuery = searchParams.get('query') || '';
    const initialCity = searchParams.get('city') || 'Everywhere';

    const [searchQuery, setSearchQuery] = useState(initialQuery);
    const [selectedCity, setSelectedCity] = useState(initialCity);

    useEffect(() => {
        setSearchQuery(initialQuery);
    }, [initialQuery]);

    useEffect(() => {
        setSelectedCity(initialCity);
    }, [initialCity]);

    const handleSearch = (query: string) => {
        setSearchParams({ query, city: selectedCity });
        setSearchQuery(query);
    };

    const handleCityChange = (city: string) => {
        setSearchParams({ query: searchQuery, city });
        setSelectedCity(city);
    };

    return (
        <div className={styles.photosPage}>
            <HeaderTraffique />
            <div className={styles.contentWrapper}>
                <Search
                    searchQuery={searchQuery}
                    selectedCity={selectedCity}
                    onSearch={handleSearch}
                    onCityChange={handleCityChange}
                />
                <div className={styles.container}>
                    <MasonryGrid searchQuery={searchQuery} selectedCity={selectedCity} />
                </div>
            </div>
        </div>
    );
};

export default MasonryPage;
