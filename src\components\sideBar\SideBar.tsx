import { Link } from 'react-router-dom';
import SidebarItem from './sideBarItem/sideBarItem';
import styles from './sidebar.module.scss'

interface SidebarProps {
    isClique?: boolean;
}

const Sidebar = ({ isClique = false }: SidebarProps) => {
    const menuItems = [
        { title: 'Photos', count: null, path: '/' },
        { title: 'Photographers', count: null, path: '/photographers' },
        { title: 'Brands', count: null, path: '/brands' },
        { title: 'Products', count: null, path: '/products' },
        { title: 'Models', count: null, path: '/models' },
        { title: 'Home', count: null, path: '/home' },
        { title: 'About', count: null, path: '/about-us' },
        { title: 'How it Works', count: null, path: '/how-it-works' },
        { title: 'Quick Chat', count: null, path: '/quick-chat' },
        { title: 'Privacy Policy', count: null, path: '/privacy-policy' },
        { title: 'Clique', count: null, path: '/clique' },
        { title: 'Data', count: null, path: '/charts' },
        { title: 'Form', count: null, path: '/form' },
    ];

    const cliqueMenuItems = [
        { title: 'Dashboard', path: '/clique' },
        { title: 'Videos', path: '/clique' },
        { title: 'Clients', path: '/' },
        { title: 'Brands', path: '/movieBrands' },
        { title: 'Advertisers', path: '/' },
        { title: 'Products', path: '/movieProducts' },
        { title: 'CMS', path: '/' },
        { title: 'Meta/List', path: '/' },
    ]

    return (
        <div className={`${styles.sidebar} ${isClique ? styles.sidebarClique : ''}`}>
            {isClique ?
                cliqueMenuItems.map((item, index) => (
                    <Link key={index} to={item.path || '#'} className={styles.sidebarLink}>
                        <SidebarItem title={item.title} count={null} />
                    </Link>
                ))
                :
                menuItems.map((item, index) => (
                    <Link key={index} to={item.path || '#'} className={styles.sidebarLink}>
                        <SidebarItem title={item.title} count={item.count} />
                    </Link>
                ))
            }
        </div>
    );
};

export default Sidebar;
