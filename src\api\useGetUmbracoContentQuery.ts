import axios from "axios";
import { useQuery, UseQueryOptions } from "@tanstack/react-query";
import { UMBRACO_ADRESS } from '../constants/urls';

export const useGetUmbracoContentQuery = (
  contentId: string,
  options?: UseQueryOptions<any, any>
) => {
  const getUmbracoContent = async (): Promise<any> => {
    try {
      const response = await axios.get<any>(
        `${UMBRACO_ADRESS}umbraco/delivery/api/v2/content/item/${contentId}`
      );
      return response.data;
    } catch (error: any) {
      const status = error.response?.status || 500;
      throw new Error(`Error fetching content with id ${contentId}: ${status}`);
    }
  };

  return useQuery<any, any>({
    queryKey: ["getUmbracoContent", contentId],
    queryFn: getUmbracoContent,
    enabled: !!contentId,
    ...options,
  });
};
