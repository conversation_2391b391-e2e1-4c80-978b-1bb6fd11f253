import React from "react";
import {
  Dialog,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  But<PERSON>,
} from "@mui/material";

interface SaveConfirmationModalProps {
  open: boolean;
  onClose: () => void;
}

const SaveConfirmationModal: React.FC<SaveConfirmationModalProps> = ({
  open,
  onClose,
}) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogContent>
        Content has been saved succesfully!
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>
          Ok
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SaveConfirmationModal;
