import { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  TextField,
  Button,
  Typography,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import styles from './sidebarPanel.module.scss'

interface SidebarFormProps {
  open: boolean;
  onClose: () => void;
}

const initialFormData = {
  idNumber: "",
  hotspotName: "",
  productName: "",
  displayedName: "",
  brand: "",
  link: "",
  zIndex: "",
};

const SidebarForm: React.FC<SidebarFormProps> = ({ open, onClose }) => {
  const [formData, setFormData] = useState(initialFormData);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!open) {
      setFormData(initialFormData);
      setSelectedFile(null);
    }
  }, [open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleThumbnailClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = () => {
    const allData = {
      ...formData,
      selectedFile,
    };
    console.log("All data submitted:", allData);
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <div className={styles.sidebar}>
        <div className={styles.topSecion}>
          <div className={styles.header}>
            <h6>BLOCK EDITOR</h6>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </div>

          <div className={styles.formContainer}>
            <div className={styles.thumbnailContainer}>
              <p className={styles.thumbnailTitle}>Thumbnail</p>
              <div
                className={`${styles.thumbnailArea} ${dragActive ? styles.active : ""}`}
                onClick={handleThumbnailClick}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                {selectedFile ? (
                  <img
                    src={URL.createObjectURL(selectedFile)}
                    alt="Thumbnail"
                    style={{ width: "100%", height: "100%", objectFit: "cover" }}
                  />
                ) : (
                  <div className={styles.icon}>
                    <AddIcon />
                    <p>Choose</p>
                  </div>
                )}
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  style={{ display: "none" }}
                  accept="image/*"
                />
              </div>
            </div>

            <div className={styles.form}>
              <div className={styles.textFieldContainer}>
                <p>ID Number</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="idNumber"
                  onChange={handleChange}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Name</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="hotspotName"
                  onChange={handleChange}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Product Name</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="productName"
                  onChange={handleChange}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Displayed Name</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="displayedName"
                  onChange={handleChange}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Brand</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="brand"
                  onChange={handleChange}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Link</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="link"
                  onChange={handleChange}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Zindex</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="zIndex"
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>
        </div>

        <div className={styles.actions}>
          <Button className={styles.cancel} onClick={onClose}>
            Cancel
          </Button>
          <Button className={styles.submit} onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default SidebarForm;
