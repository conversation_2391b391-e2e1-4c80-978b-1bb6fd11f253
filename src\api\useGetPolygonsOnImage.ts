import { useState, useCallback } from "react";
import { Polygon } from "../types/polygons.type";
import { RUNPOD_API_URL, RUNPOD_API_KEY } from "../constants/urls";

interface GetPolygonsOnImageParams {
  imageUrl: string;
  selections: any;
}

export function useGetPolygonsOnImage() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const getPolygonsOnImage = useCallback(
    async ({
      imageUrl,
      selections,
    }: GetPolygonsOnImageParams): Promise<Polygon[]> => {
      console.log("sendApiRequest called");
      setIsProcessing(true);
      setError(null);

      const requestData = {
        input: {
          imageUrl,
          selections,
        },
      };

      try {
        const response = await fetch(`${RUNPOD_API_URL}runsync`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${RUNPOD_API_KEY}`,
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const json = await response.json();
        return json.output?.output?.polygons ?? [];
      } catch (err) {
        console.error("API request failed:", err);
        setError(err instanceof Error ? err : new Error("Unknown error"));
        return [];
      } finally {
        setIsProcessing(false);
      }
    },
    []
  );

  return { getPolygonsOnImage, isProcessing, error };
}
